// TODO: Implement from Scratch:
// TODO: A function to find an element in an array (linear search).
// TODO: A function to insert an element at a specific index.
// TODO: A function to delete an element at a specific index.

pub fn find_element(array: &[i32], target: i32) -> Option<usize> {
    let mut result: Option<usize> = None; 
    for (index, item) in array {
        if *item == target {
            result = Some(index);
            break;
        }
    }
    result
}