// Array Operations Implementation from Scratch
// 1. Linear search to find an element
// 2. Insert element at specific index
// 3. Delete element at specific index

/// Linear search: Find the first occurrence of target element in array
/// Returns Some(index) if found, None if not found
/// Time complexity: O(n), Space complexity: O(1)
pub fn find_element(array: &[i32], target: i32) -> Option<usize> {
    // Manual implementation without using built-in functions
    let mut index: usize = 0;
    while index < array.len() {
        if array[index] == target {
            return Some(index);
        }
        index += 1;
    }
    None
}

/// Insert element at specific index in a vector
/// Returns Result with new vector if successful, or error message if index is invalid
/// Time complexity: O(n), Space complexity: O(n)
pub fn insert_element(array: Vec<i32>, element: i32, index: usize) -> Result<Vec<i32>, String> {
    // Check if index is valid (can be 0 to array.len() inclusive for insertion)
    if index > array.len() {
        return Err(format!("Index {} is out of bounds for array of length {}", index, array.len()));
    }

    // Create new vector with capacity for one more element
    let mut new_array = Vec::with_capacity(array.len() + 1);

    // Manual implementation: copy elements before insertion point
    let mut i = 0;
    while i < index {
        new_array.push(array[i]);
        i += 1;
    }

    // Insert the new element
    new_array.push(element);

    // Copy remaining elements after insertion point
    while i < array.len() {
        new_array.push(array[i]);
        i += 1;
    }

    Ok(new_array)
}

/// Delete element at specific index from a vector
/// Returns Result with new vector if successful, or error message if index is invalid
/// Time complexity: O(n), Space complexity: O(n)
pub fn delete_element(array: Vec<i32>, index: usize) -> Result<Vec<i32>, String> {
    // Check if index is valid
    if index >= array.len() {
        return Err(format!("Index {} is out of bounds for array of length {}", index, array.len()));
    }

    // Handle empty array after deletion
    if array.len() == 1 {
        return Ok(Vec::new());
    }

    // Create new vector with capacity for one less element
    let mut new_array = Vec::with_capacity(array.len() - 1);

    // Manual implementation: copy elements before deletion point
    let mut i = 0;
    while i < index {
        new_array.push(array[i]);
        i += 1;
    }

    // Skip the element at deletion index (i.e., don't copy it)
    i += 1;

    // Copy remaining elements after deletion point
    while i < array.len() {
        new_array.push(array[i]);
        i += 1;
    }

    Ok(new_array)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_find_element() {
        // Test finding existing elements
        let array = [1, 3, 5, 7, 9, 11];
        assert_eq!(find_element(&array, 1), Some(0));
        assert_eq!(find_element(&array, 5), Some(2));
        assert_eq!(find_element(&array, 11), Some(5));

        // Test finding non-existing element
        assert_eq!(find_element(&array, 4), None);
        assert_eq!(find_element(&array, 0), None);
        assert_eq!(find_element(&array, 12), None);

        // Test empty array
        let empty: [i32; 0] = [];
        assert_eq!(find_element(&empty, 1), None);

        // Test single element array
        let single = [42];
        assert_eq!(find_element(&single, 42), Some(0));
        assert_eq!(find_element(&single, 43), None);
    }

    #[test]
    fn test_insert_element() {
        // Test inserting at beginning
        let array = vec![2, 3, 4];
        let result = insert_element(array, 1, 0).unwrap();
        assert_eq!(result, vec![1, 2, 3, 4]);

        // Test inserting in middle
        let array = vec![1, 3, 4];
        let result = insert_element(array, 2, 1).unwrap();
        assert_eq!(result, vec![1, 2, 3, 4]);

        // Test inserting at end
        let array = vec![1, 2, 3];
        let result = insert_element(array, 4, 3).unwrap();
        assert_eq!(result, vec![1, 2, 3, 4]);

        // Test inserting into empty array
        let array = vec![];
        let result = insert_element(array, 1, 0).unwrap();
        assert_eq!(result, vec![1]);

        // Test invalid index
        let array = vec![1, 2, 3];
        let result = insert_element(array, 4, 5);
        assert!(result.is_err());
    }

    #[test]
    fn test_delete_element() {
        // Test deleting from beginning
        let array = vec![1, 2, 3, 4];
        let result = delete_element(array, 0).unwrap();
        assert_eq!(result, vec![2, 3, 4]);

        // Test deleting from middle
        let array = vec![1, 2, 3, 4];
        let result = delete_element(array, 2).unwrap();
        assert_eq!(result, vec![1, 2, 4]);

        // Test deleting from end
        let array = vec![1, 2, 3, 4];
        let result = delete_element(array, 3).unwrap();
        assert_eq!(result, vec![1, 2, 3]);

        // Test deleting from single element array
        let array = vec![42];
        let result = delete_element(array, 0).unwrap();
        assert_eq!(result, vec![]);

        // Test invalid index
        let array = vec![1, 2, 3];
        let result = delete_element(array, 5);
        assert!(result.is_err());

        // Test deleting from empty array
        let array = vec![];
        let result = delete_element(array, 0);
        assert!(result.is_err());
    }
}

