use arr_str::{find_element, insert_element, delete_element};

fn main() {
    println!("=== Array Operations Demo ===\n");

    // Demo 1: Linear Search
    let array = [10, 25, 30, 45, 50, 65];
    println!("Original array: {:?}", array);

    match find_element(&array, 30) {
        Some(index) => println!("Found 30 at index: {}", index),
        None => println!("30 not found"),
    }

    match find_element(&array, 99) {
        Some(index) => println!("Found 99 at index: {}", index),
        None => println!("99 not found"),
    }

    // Demo 2: Insert Element
    println!("\n--- Insert Operations ---");
    let mut vec = vec![1, 2, 4, 5];
    println!("Original vector: {:?}", vec);

    match insert_element(vec.clone(), 3, 2) {
        Ok(new_vec) => {
            println!("After inserting 3 at index 2: {:?}", new_vec);
            vec = new_vec;
        },
        Err(e) => println!("Insert error: {}", e),
    }

    match insert_element(vec.clone(), 0, 0) {
        Ok(new_vec) => {
            println!("After inserting 0 at index 0: {:?}", new_vec);
            vec = new_vec;
        },
        Err(e) => println!("Insert error: {}", e),
    }

    // Demo 3: Delete Element
    println!("\n--- Delete Operations ---");
    println!("Current vector: {:?}", vec);

    match delete_element(vec.clone(), 0) {
        Ok(new_vec) => {
            println!("After deleting element at index 0: {:?}", new_vec);
            vec = new_vec;
        },
        Err(e) => println!("Delete error: {}", e),
    }

    match delete_element(vec.clone(), 2) {
        Ok(new_vec) => {
            println!("After deleting element at index 2: {:?}", new_vec);
        },
        Err(e) => println!("Delete error: {}", e),
    }

    // Demo error handling
    println!("\n--- Error Handling Demo ---");
    let small_vec = vec![1, 2];
    match insert_element(small_vec.clone(), 99, 10) {
        Ok(_) => println!("Insert succeeded"),
        Err(e) => println!("Insert error: {}", e),
    }

    match delete_element(small_vec, 10) {
        Ok(_) => println!("Delete succeeded"),
        Err(e) => println!("Delete error: {}", e),
    }
}
